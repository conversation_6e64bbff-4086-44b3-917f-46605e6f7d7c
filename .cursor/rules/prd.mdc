---
description: 
globs: 
alwaysApply: false
---
# 📄 Product Requirements Document (PRD)

## 🧩 프로젝트 개요

### 주제
> **“대전 11개 대학생을 위한 신뢰 기반 AI 소개팅 플랫폼”**  
대학생들이 안전하고 진정성 있는 연애를 경험할 수 있도록, 학교 인증, AI 매칭, 커뮤니티 기능, 오프라인 밋업까지 아우르는 통합형 데이팅 서비스.

### 목적
- 지역 대학생 간 건강한 교류 기회 제공
- 진정성 있는 만남을 중심으로 한 하이브리드 데이팅 플랫폼
- 대전 내 11개 대학 → 전국 확장 전략 기반

---

## 🎯 사용자 목표 (User Goals)
1. **편리하고 신뢰할 수 있는 회원가입 시스템**
   - 학교 인증 기반 가입
2. **정교한 AI 소개팅 경험**
   - 이상형 및 성향 정보 기반 매칭
3. **생동감 있는 커뮤니티**
   - 관심사 공유 및 소통 공간 제공
4. **사용자 주도적 활동 조절 기능**
   - 여성 회원의 ‘매칭 쉬기’ 기능 제공
---

## 👤 사용자 스토리 (User Stories)

### 매칭 시스템
- 매칭은 **매주 목요일/일요일 21시 공개**
- 마음에 들지 않거나 매칭 실패 시 **재매칭권(유료)**으로 즉시 매칭 시도 가능
- 매칭 상대의 정보는 **홈 화면 카드 + 상세 정보**로 확인 가능
- 상세 정보에는 MBTI, 라이프스타일, 음주/흡연/문신 여부, 연애스타일, 성격 등 포함
- **“인스타그램으로 연락하기”** 버튼으로 DM 유도
- **프로필 공개 시간 제한**:
  - 목요일 매칭: 토요일 0:00시까지 공개
  - 일요일 매칭: 화요일 0:00시까지 공개

### 회원가입 플로우
- 필수 입력: 이메일, 비밀번호, 이름, 생년월일, 성별, MBTI, 사진 3장
- 대학 정보: 대학교, 학과, 학번, 학년
- 인스타그램 ID 입력
- 이용약관 동의 후 가입

### 이상형 정보
- 선호 나잇대
- 음주/흡연/문신 여부
- 관심 있는 활동
- 연애 스타일
- (여성) 상대 군필 여부 선호
- (남성) 본인 군필 여부 입력

### 커뮤니티 기능
- 카테고리: 실시간 / 인기 / 리뷰 / 연애상담
- 좋아요, 댓글, 조회수 기반 인기글 선정
- 게시글 점수화 → 특정 시간마다 인기글 업데이트
- 댓글 및 대댓글 기능 포함

### 알림 시스템
- **이메일 기반 알림 제공 (푸시 기능 없음)**
  - 매칭 결과 공개
  - 본인 게시글 인기글 선정
  - 부적절한 활동 알림

---

## 🛠 기술 스택 (Tech Stack)

### 백엔드
- **NestJS (TypeScript)**: 모듈 기반 구조화된 서버 개발
- **PostgreSQL**: 정형 회원 및 매칭 데이터 저장
- **Qdrant**: 벡터 임베딩 기반 AI 매칭 추천 시스템
- **Redis**: 캐시, 인기글 큐 스케줄링, 알림 큐 등

### 프론트엔드
- **React Native (예정)**: Android & iOS 앱 통합 개발

### 기타 시스템
- **이메일 알림 시스템**: SMTP + Redis 기반 작업 큐 사용
- **AI 매칭 알고리즘**:
  - Qdrant 기반 벡터 유사도 검색
  - 사용자 프로필 → 벡터 임베딩 → 선호도 기반 필터링 및 랭킹화

---

## 💡 기대 효과
- 대전 지역 청년 간의 실질적인 교류 활성화
- 소셜 앱 대비 진정성과 신뢰 기반의 차별화된 브랜딩 확보
- 온/오프라인 병행 구조로 ‘가벼운 소통 → 만남’의 자연스러운 전환 유도

---
