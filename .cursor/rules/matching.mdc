---
description: 
globs: 
alwaysApply: false
---
📄 Product Requirements Document (PRD): 매칭 시스템
🧩 기능 개요
주제

"대전 지역 11개 대학생을 위한 AI 기반 매칭 시스템"
주기적인 AI 매칭, 재매칭 기능, 상세 프로필 확인 및 연락 기능을 포함하는 신뢰 기반 데이팅 플랫폼의 핵심 기능

목적

사용자 간 신뢰성 높은 매칭 제공
매칭에 실패하거나 불만족 시 재매칭 옵션 제공
사용자의 매칭 경험 최적화


🎯 핵심 기능 목표

정기적인 AI 매칭 시스템

매주 목요일/일요일 21시 매칭 결과 공개
매칭 결과는 48시간 동안 확인 가능


재매칭 시스템

매칭 실패 또는 불만족 시 재매칭권 사용 가능
주당 최대 6회 재매칭 가능 (무료 매칭 후 각 3회)


매칭 카드 및 프로필 보기

매칭 대기/성공/실패 상태에 따른 UI 변화
상세 프로필 확인 및 연락 기능




👤 사용자 시나리오
시나리오 1: 매칭 대기

사용자가 앱을 열면 홈 화면에 매칭 대기 카드 표시
D-day 또는 H-hour 형식으로 다음 매칭까지 남은 시간 확인
매주 목요일/일요일 21시까지 대기

시나리오 2: 매칭 성공

매칭 시간이 되면 매칭된 상대의 프로필 카드 표시
상대방의 메인 사진, 나이, MBTI, 대학교 정보 확인
"더보기"로 상세 프로필 확인 가능
"연락하기"로 상대방 인스타그램 ID 접근 가능
"재매칭권 사용하기"로 새로운 매칭 요청 가능

시나리오 3: 매칭 실패

매칭 실패 시 매칭 실패 카드 표시
실패 이유 확인 가능
"재매칭권 사용하기"만 활성화 상태로 표시

시나리오 4: 재매칭 사용

재매칭권 사용 시 확인 모달 표시
재매칭 처리 중 로딩 화면 표시 (3-5초)
재매칭 성공 시 새로운 매칭 카드 상단에 표시
기존 매칭 카드는 하단에 유지


📱 UI 상태 및 화면 설계
1. 매칭 대기 카드

상태: 다음 매칭까지 대기 중
표시 요소:

디데이 카운터(D-00 형식)
24시간 이내 시 시간 카운터(H-00 형식)
"매주 목·일 21시에 매칭이 시작돼요!" 메시지



2. 매칭 카드

상태: 매칭 성공
표시 요소:

상대방 메인 사진
나이, MBTI, 대학교 정보
대학 인증 마크
매칭 후 경과 시간(H+00 형식)
"더보기" 버튼 - 상세 프로필로 이동
"재매칭권 사용하기" 버튼
"연락하기" 버튼



3. 매칭 실패 카드

상태: 매칭 실패
표시 요소:

"매칭에 실패했어요" 메시지
실패 이유 설명
"재매칭권 사용하기" 버튼(활성화)
"더보기", "연락하기" 버튼(비활성화)



4. 재매칭 진행 카드

상태: 재매칭 처리 중
표시 요소:

"잠시만 기다려주세요" 메시지
"재매칭권을 사용하고 있어요..." 메시지
로딩 애니메이션



5. 재매칭 결과 카드

상태: 재매칭 성공
표시 요소:

재매칭된 상대방 프로필
매칭 경과 시간(재매칭 시점부터 계산)
기존 매칭 정보(하단에 표시)




🔄 매칭 로직 및 비즈니스 규칙
매칭 타이밍

매주 목요일/일요일 21:00에 정기 매칭 진행
목요일 매칭: 토요일 0:00까지 공개 (약 27시간)
일요일 매칭: 화요일 0:00까지 공개 (약 27시간)

재매칭 규칙

재매칭권 사용 제한:

무료 매칭 이후 최대 3회까지 사용 가능
주당 총 6회(목요일 3회, 일요일 3회) 사용 가능


재매칭 결과:

재매칭 사용 시 100% 매칭 성공
매칭 성공 후 재매칭 시: 한 단계 상향된 등급 풀에서 매칭
매칭 실패 후 재매칭 시: 기본 매칭 프로세스 진행


매칭 실패 조건:

원칙적으로 C/미분류 등급에서만 발생 가능
적절한 매칭 대상이 없는 경우 예외적으로 실패 가능



프로필 표시 규칙

최대 4개 프로필까지 홈에 표시 가능(무료 1개, 재매칭 3개)
재매칭된 프로필은 상단에 표시, 이전 매칭은 하단에 유지
추가로 매칭을 받은 사용자는 최대 3명까지 추가 프로필 확인 가능


📊 기술 구현 요구사항
백엔드 요구사항

매칭 시스템:

정해진 시간(목/일 21:00)에 배치 처리로 매칭 실행
Qdrant 벡터 임베딩 기반 AI 매칭 알고리즘 활용
Redis를 활용한 매칭 큐 및 스케줄링 처리


재매칭 시스템:

실시간 재매칭 요청 처리 API
사용자 재매칭권 관리 및 차감 로직
등급 풀 상향 매칭 로직


데이터 관리:

매칭 이력 저장 및 관리
사용자 프로필 및 매칭 선호도 정보 관리
매칭 만료 시간 관리



프론트엔드 요구사항

UI 상태 관리:

매칭 상태에 따른 카드 UI 변경
타이머 기능(D-day, 시간 카운터)
매칭 경과 시간 표시


사용자 인터랙션:

더보기, 재매칭권 사용하기, 연락하기 버튼 기능
재매칭 확인 모달 및 로딩 처리
인스타그램 연결 또는 ID 복사 기능


프로필 카드 관리:

최대 4개 카드 관리 및 순서 정렬
여러 매칭 카드 간 슬라이드 네비게이션




📅 구현 우선순위 및 단계
Phase 1: 기본 매칭 시스템 (SOM-001)

정기 매칭 알고리즘 구현
매칭 대기 및 결과 카드 UI 구현
매칭 타이머 및 경과 시간 표시 기능

Phase 2: 재매칭 시스템 (SOM-002)

재매칭권 관리 시스템
재매칭 요청 및 처리 기능
재매칭 카드 및 모달 UI

Phase 3: 프로필 및 연락 기능 (SOM-003)

상세 프로필 보기 기능
인스타그램 연결 기능
매칭 실패 처리 및 안내 메시지


🔍 품질 요구사항 및 테스트 계획
성능 요구사항

매칭 알고리즘 처리 시간: 목표 < 5초/사용자
재매칭 처리 시간: 목표 < 3초
UI 반응 속도: 목표 < 0.5초

테스트 시나리오

정기 매칭 테스트:

다양한 사용자 프로필로 매칭 알고리즘 검증
시간 기반 매칭 공개 기능 검증


재매칭 테스트:

재매칭권 소진 시나리오
연속 재매칭 시나리오
등급 상향 매칭 검증


UI 상태 테스트:

다양한 매칭 상태에서의 UI 검증
타이머 및 경과 시간 정확성 검증




📈 성공 지표

매칭 성공률:

목표: 90% 이상의 사용자가 첫 매칭에서 성공


재매칭 사용률:

목표: 30% 이하의 사용자가 재매칭 사용


연락 전환율:

목표: 매칭된 사용자의 60% 이상이 연락하기 기능 사용


사용자 만족도:

목표: 매칭 품질에 대한 사용자 만족도 4.0/5.0 이상




📝 개발 노트

매칭 알고리즘은 Qdrant 벡터 임베딩 기반으로 구현
매칭 및 재매칭 이력은 PostgreSQL에 저장
Redis를 활용하여 매칭 스케줄링 및 캐싱 최적화
최대 4개의 프로필 카드를 효율적으로 관리할 수 있는 UI 설계 필요


이 PRD는 SOM(Sometimes) 프로젝트의 매칭 시스템 구현을 위한 요구사항과 설계를 담고 있습니다. 모든 구현은 백엔드(NestJS)와 프론트엔드(React Native) 간의 긴밀한 협업을 통해 진행됩니다.