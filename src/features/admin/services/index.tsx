import { router } from "expo-router";
import { useAuth } from "../../auth";

export const excludeEmails = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

export const loginProduction = (email: string) => {
  if (excludeEmails.includes(email)) {
    router.navigate('/home');
    return;
  }
  router.navigate('/commingsoon');
};

export const doAdmin = (email: string,callback:() => void, noAdminCallback?:() => void) => {
  if (email && excludeEmails.includes(email)) {
    callback();
  } else {
    noAdminCallback?.();
  }
}