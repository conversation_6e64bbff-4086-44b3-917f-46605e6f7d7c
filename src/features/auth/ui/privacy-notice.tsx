import {StyleSheet, View, TouchableOpacity, Linking} from 'react-native';
import {Text} from '@ui/text';
import colors from '@constants/colors';

const PRIVACY_LINK = 'https://ruby-composer-6d2.notion.site/1cd1bbec5ba180a3a4bbdf9301683145';
const SERVICE_LINK = 'https://ruby-composer-6d2.notion.site/1cd1bbec5ba1805dbafbc9426a0aaa80';
const ENTERPIRSE_LINK = 'http://www.ftc.go.kr/bizCommPop.do?wrkr_no=4980502914';

export const PrivacyNotice = () => (
    <View className="flex flex-col w-full items-center">
      <Text textColor="gray" className="text-[12px]">
        회원가입 및 로그인 버튼을 누르면
      </Text>

      <View className="flex flex-row">
        <TouchableOpacity onPress={() => Linking.openURL(PRIVACY_LINK)}>
          <Text
              style={{
                ...styles.link,
                marginRight: 2,
              }}
          >
            개인정보 보호 약관,
          </Text>
        </TouchableOpacity>

        <TouchableOpacity onPress={() => Linking.openURL(SERVICE_LINK)}>
          <Text style={{...styles.link}}>서비스 이용약관</Text>
        </TouchableOpacity>

        <Text textColor="gray" className="text-[12px]">
          에 동의하게 됩니다.
        </Text>
      </View>

      <TouchableOpacity onPress={() => Linking.openURL(ENTERPIRSE_LINK)}>
        <Text
            style={{
              ...styles.link,
              marginTop: 10,
              color: colors.gray,
            }}
        >
          스마트 뉴비 사업자 정보 {'>'}
        </Text>
      </TouchableOpacity>
    </View>
);

const styles = StyleSheet.create({
  link: {
    textDecorationLine: 'underline',
    fontSize: 12,
    color: colors['gray-600'],
    fontWeight: '600',
  },
});
