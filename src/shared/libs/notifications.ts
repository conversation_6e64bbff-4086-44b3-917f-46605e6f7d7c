import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import { Platform } from 'react-native';
import axiosClient from './axios';

// 알림 핸들러 설정
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

/**
 * 푸시 알림 권한을 요청하고 토큰을 획득하여 백엔드에 등록합니다.
 * @param userId 사용자 ID
 * @returns 푸시 토큰 또는 null
 */
export async function registerForPushNotificationsAsync(userId: string): Promise<string | null> {
  let token: string | null = null;

  // Android 알림 채널 설정
  if (Platform.OS === 'android') {
    await Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
      sound: 'default',
    });

    // 커뮤니티 알림 채널
    await Notifications.setNotificationChannelAsync('community', {
      name: '커뮤니티 알림',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
      sound: 'default',
      description: '댓글, 좋아요 등 커뮤니티 활동 알림',
    });
  }

  // 실제 기기에서만 푸시 알림 사용 가능
  if (Device.isDevice) {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;
    
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }
    
    if (finalStatus !== 'granted') {
      console.warn('푸시 알림 권한이 거부되었습니다.');
      return null;
    }
    
    try {
      const projectId = Constants.expoConfig?.extra?.eas?.projectId;
      if (!projectId) {
        console.error('EAS 프로젝트 ID가 설정되지 않았습니다.');
        return null;
      }

      const pushTokenData = await Notifications.getExpoPushTokenAsync({
        projectId,
      });
      
      token = pushTokenData.data;
      
      // 백엔드에 토큰 등록
      await registerPushToken(userId, token);
      
      console.log('푸시 토큰 등록 성공:', token);
    } catch (error) {
      console.error('푸시 토큰 획득 실패:', error);
      return null;
    }
  } else {
    console.warn('실제 기기에서만 푸시 알림을 사용할 수 있습니다.');
  }

  return token;
}

/**
 * 백엔드에 푸시 토큰을 등록합니다.
 * @param userId 사용자 ID
 * @param pushToken 푸시 토큰
 */
async function registerPushToken(userId: string, pushToken: string): Promise<void> {
  try {
    await axiosClient.post('/api/push-notifications/register-token', {
      userId,
      pushToken,
      deviceId: Constants.deviceId || 'unknown',
      platform: Platform.OS,
    });

    console.log('푸시 토큰 백엔드 등록 성공');
  } catch (error) {
    console.error('푸시 토큰 백엔드 등록 실패:', error);
    throw error;
  }
}

/**
 * 푸시 토큰을 백엔드에서 제거합니다. (로그아웃 시 사용)
 * @param userId 사용자 ID
 */
export async function unregisterPushToken(userId: string): Promise<void> {
  try {
    const deviceId = Constants.deviceId || 'unknown';
    await axiosClient.delete('/api/push-notifications/register-token', {
      data: {
        userId,
        deviceId,
        platform: Platform.OS,
      }
    });

    console.log('푸시 토큰 제거 성공');
  } catch (error) {
    console.error('푸시 토큰 제거 실패:', error);
  }
}

/**
 * 알림 권한 상태를 확인합니다.
 * @returns 권한 상태
 */
export async function getNotificationPermissionStatus(): Promise<Notifications.PermissionStatus> {
  const { status } = await Notifications.getPermissionsAsync();
  return status;
}

/**
 * 알림 권한을 다시 요청합니다.
 * @returns 권한 상태
 */
export async function requestNotificationPermission(): Promise<Notifications.PermissionStatus> {
  const { status } = await Notifications.requestPermissionsAsync();
  return status;
}

/**
 * 알림 데이터 타입 정의
 */
export interface NotificationData {
  type: 'comment' | 'like' | 'general';
  articleId?: string;
  commentId?: string;
  userId?: string;
  title: string;
  body: string;
}

/**
 * 알림 탭 시 적절한 화면으로 이동하는 핸들러
 * @param data 알림 데이터
 * @param router Expo Router 인스턴스
 */
export function handleNotificationTap(data: NotificationData, router: any): void {
  try {
    switch (data.type) {
      case 'comment':
        if (data.articleId) {
          router.push(`/community/article/${data.articleId}`);
        }
        break;
      case 'like':
        if (data.articleId) {
          router.push(`/community/article/${data.articleId}`);
        }
        break;
      case 'general':
        // 일반 알림의 경우 홈으로 이동
        router.push('/home');
        break;
      default:
        console.warn('알 수 없는 알림 타입:', data.type);
        router.push('/home');
    }
  } catch (error) {
    console.error('알림 탭 처리 중 오류:', error);
    router.push('/home');
  }
}

/**
 * 테스트 푸시 알림을 전송합니다. (개발용)
 */
export async function sendTestNotification(): Promise<void> {
  try {
    await axiosClient.post('/api/push-notifications/test');
    console.log('테스트 알림 전송 성공');
  } catch (error) {
    console.error('테스트 알림 전송 실패:', error);
    throw error;
  }
}
