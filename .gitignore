# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

.idea

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

android
ios

# debug
npm-debug.*
yarn-debug.*
yarn-error.*
.env
# macOS
.DS_Store
*.pem

# local env files
.env*.develop
.env.production
.env.local

# typescript
*.tsbuildinfo

app-example
.vercel
.vscode

review.md