import { Stack } from 'expo-router';
import { View } from 'react-native';

export default function MyLayout() {
  return (
    <View className="flex-1">
      <Stack>
        <Stack.Screen 
          name="index" 
          options={{ 
            headerShown: false,
            contentStyle: {
              backgroundColor: 'transparent',
            },
          }} 
        />
        <Stack.Screen 
          name="withdrawal" 
          options={{ 
            headerShown: false,
            contentStyle: {
              backgroundColor: 'transparent',
            },
          }} 
        />
      </Stack>
    </View>
  );
}
