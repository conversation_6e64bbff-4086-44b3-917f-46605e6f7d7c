import { useFonts } from "expo-font";
import { Slot, router } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { useEffect, useRef } from "react";
import { Platform, View } from "react-native";
import "react-native-reanimated";
import "../global.css";
import * as Notifications from 'expo-notifications';
import { handleNotificationTap, type NotificationData } from "@/src/shared/libs/notifications";

import { PortoneProvider } from "@/src/features/payment/hooks/PortoneProvider";
import ProfileDrinking from "@/src/features/profile-edit/ui/profile/profile-drinking";
import { VersionUpdateChecker } from "@/src/features/version-update";
import { QueryProvider, RouteTracker } from "@/src/shared/config";
import { useAtt } from "@/src/shared/hooks";
import { useColorScheme } from "@/src/shared/hooks/use-color-schema";
import { cn } from "@/src/shared/libs/cn";
import { Analytics<PERSON>rovider, ModalProvider } from "@/src/shared/providers";
import { GestureHandlerRootView } from "react-native-gesture-handler";

SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const { request: requestAtt } = useAtt();
  const notificationListener = useRef<Notifications.EventSubscription>();
  const responseListener = useRef<Notifications.EventSubscription>();

  const [loaded] = useFonts({
    "Pretendard-Thin": require("../assets/fonts/Pretendard-Thin.ttf"),
    "Pretendard-ExtraLight": require("../assets/fonts/Pretendard-ExtraLight.ttf"),
    "Pretendard-SemiBold": require("../assets/fonts/Pretendard-SemiBold.ttf"),
    "Pretendard-ExtraBold": require("../assets/fonts/Pretendard-ExtraBold.ttf"),
    "Pretendard-Bold": require("../assets/fonts/Pretendard-Bold.ttf"),
    "Pretendard-Black": require("../assets/fonts/Pretendard-Black.ttf"),
    Rubik: require("../assets/fonts/Rubik-Regular.ttf"),
    "Rubik-Medium": require("../assets/fonts/Rubik-Medium.ttf"),
    "Rubik-Bold": require("../assets/fonts/Rubik-Bold.ttf"),
    "Rubik-Light": require("../assets/fonts/Rubik-Light.ttf"),
    "Rubik-SemiBold": require("../assets/fonts/Rubik-SemiBold.ttf"),
  });

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  useEffect(() => {
    requestAtt();
  }, []);

  // 푸시 알림 리스너 설정
  useEffect(() => {
    // 앱이 포그라운드에 있을 때 알림 수신
    notificationListener.current = Notifications.addNotificationReceivedListener(notification => {
      console.log('알림 수신:', notification);
      // 포그라운드에서 알림을 받았을 때의 처리 로직
      // 필요시 커스텀 알림 UI 표시 등
    });

    // 사용자가 알림을 탭했을 때
    responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
      const rawData = response.notification.request.content.data;

      console.log('알림 탭:', rawData);

      // 데이터 검증 후 타입 캐스팅
      if (rawData &&
          typeof rawData === 'object' &&
          'type' in rawData &&
          'title' in rawData &&
          'body' in rawData) {
        const data = rawData as unknown as NotificationData;
        handleNotificationTap(data, router);
      } else {
        // 데이터가 없거나 형식이 맞지 않으면 홈으로 이동
        router.push('/home');
      }
    });

    return () => {
      if (notificationListener.current) {
        notificationListener.current.remove();
      }
      if (responseListener.current) {
        responseListener.current.remove();
      }
    };
  }, []);

  useEffect(() => {
    // Hotjar는 웹에서만 초기화 (Android 빌드 문제 방지)
    if (Platform.OS === "web") {
      try {
        const script = document.createElement("script");
        script.innerHTML = `
          (function(h,o,t,j,a,r){
            h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
            h._hjSettings={hjid:6430952,hjsv:6};
            a=o.getElementsByTagName('head')[0];
            r=o.createElement('script');r.async=1;
            r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
            a.appendChild(r);
          })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
        `;
        document.head.appendChild(script);

        if (__DEV__) {
          console.log("Hotjar script loaded in development mode.");
        }
      } catch (error) {
        console.warn("Failed to load Hotjar:", error);
      }
    }
  }, []);

  if (!loaded) {
    return null;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <QueryProvider>
        <ModalProvider>
          <PortoneProvider>
            <View
              className={cn(
                "flex-1 font-extralight",
                Platform.OS === "web" && "max-w-[468px] w-full self-center"
              )}
            >
              <AnalyticsProvider>
                <RouteTracker>
                  <>
                    <Slot />
                    <VersionUpdateChecker />
                  </>
                </RouteTracker>
              </AnalyticsProvider>
            </View>
          </PortoneProvider>
        </ModalProvider>
      </QueryProvider>
    </GestureHandlerRootView>
  );
}
