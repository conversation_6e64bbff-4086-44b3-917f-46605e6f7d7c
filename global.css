@tailwind base;
@tailwind components;
@tailwind utilities;
@import url('https://fonts.googleapis.com/css2?family=Rubik:ital,wght@0,300..900;1,300..900&display=swap');

:root {
  --primary-purple: #A78BFA;
  /* 주요 보라색 */
  --light-purple: #EDE9FE;
  /* 배경에 사용된 연한 보라색 */
  --dark-purple: #7C3AED;
  /* 버튼에 사용된 진한 보라색 */
  --white: #FFFFFF;
  /* 텍스트 및 배경에 사용된 흰색 */
  --black: #000000;
  /* 텍스트에 사용된 검정색 */
  --gray: #9CA3AF;
  /* 입력 필드의 힌트 텍스트에 사용된 회색 */
}

.purple-gradient-bg {
  background: linear-gradient(180deg, #FFF 0%, #F9F7FF 100%);
}

input {
  transform: scale(1);
  /* iOS에서 추가적인 확대 방지 */
}