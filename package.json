{"name": "sometimes", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start --port 3000", "start:prod": "NODE_ENV=production expo start --port 3000", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android --port 3000", "ios": "expo run:ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watchAll", "lint": "expo lint"}, "dependencies": {"@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@expo/config-plugins": "^10.0.2", "@expo/vector-icons": "^14.0.2", "@hookform/resolvers": "^5.0.1", "@hotjar/browser": "^1.0.9", "@lottiefiles/dotlottie-react": "^0.13.5", "@portone/browser-sdk": "^0.0.17", "@portone/react-native-sdk": "^0.5.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-masked-view/masked-view": "^0.3.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/elements": "^2.3.8", "@react-navigation/material-top-tabs": "^7.3.2", "@react-navigation/native": "^7.0.14", "@tanstack/react-query": "^5.73.3", "autoprefixer": "^10.4.21", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "expo": "53.0.12", "expo-application": "~6.1.5", "expo-blur": "~14.1.4", "expo-calendar": "~14.1.4", "expo-camera": "~16.1.6", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.1", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "^2.1.7", "expo-image-picker": "~16.1.4", "expo-intent-launcher": "~12.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-media-library": "~17.1.7", "expo-router": "~5.1.0", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.9", "expo-tracking-transparency": "~5.2.4", "expo-web-browser": "~14.1.6", "iamport": "^0.3.4", "metro-react-native-babel-transformer": "^0.76.9", "nanoid": "^5.1.5", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.55.0", "react-native": "0.79.4", "react-native-css-interop": "^0.1.22", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-progress": "^5.0.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-svg-transformer": "^1.5.1", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "tailwind-merge": "^3.2.0", "tailwindcss": "^3.4.17", "ts-pattern": "^5.7.1", "tw-merge": "^0.0.1-alpha.3", "zod": "^3.24.2", "zustand": "^5.0.3", "expo-notifications": "~0.31.4", "expo-device": "~7.1.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@biomejs/biome": "1.9.4", "@testing-library/react-native": "^13.2.0", "@types/invariant": "^2.2.37", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.20", "@types/react": "~19.0.10", "@types/react-native-get-random-values": "^1.8.2", "@types/react-test-renderer": "^18.3.0", "eslint": "^9.0.0", "eslint-config-expo": "~9.2.0", "jest": "^29.2.1", "jest-expo": "~53.0.7", "react-test-renderer": "19.0.0", "ts-jest": "^29.3.2", "typescript": "^5.3.3"}, "private": true}