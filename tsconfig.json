{
  "extends": "expo/tsconfig.base",
  "compilerOptions": {
    "module": "NodeNext",
    "moduleResolution": "NodeNext",
    "strict": true,
    "isolatedModules": true,
    "jsx": "react-jsx",
    "esModuleInterop": true,
    "types": [
      "react-native",
      "jest",
      "node"
    ],
    "paths": {
      "@/*": [
        "./*",
      ],
      "@features/*": [
        "./src/features/*"
      ],
      "@hooks/*": [
        "./src/shared/hooks/*"
      ],
      "@utils/*": [
        "./src/shared/utils/*"
      ],
      "@constants/*": [
        "./src/shared/constants/*"
      ],
      "@ui/*": [
        "./src/shared/ui/*"
      ],
      "@entities/*": [
        "./src/entities/*"
      ],
      "@widgets/*": [
        "./src/widgets/*"
      ],
      "@shared/*": [
        "./src/shared/*"
      ],
      "@assets/*": [
        "./assets/*"
      ],
      "@types/*": [
        "./src/types/*"
      ]
    }
  },
  "include": [
    "**/*.ts",
    "**/*.tsx",
    ".expo/types/**/*.ts",
    "expo-env.d.ts",
    "nativewind-env.d.ts",
    "src/types/iamport.d.ts"
  ]
}