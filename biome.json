{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": ["node_modules", "dist", "build", "public", "src/shared/ui/icons"]}, "formatter": {"enabled": true, "indentStyle": "tab", "indentWidth": 2, "lineWidth": 100}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"useExhaustiveDependencies": "warn"}, "style": {"useLiteralEnumMembers": "off"}}}, "javascript": {"formatter": {"quoteStyle": "single"}}}